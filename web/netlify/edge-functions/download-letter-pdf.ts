import type { Config } from "@netlify/edge-functions";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// The Netlify Edge runtime provides a global `Netlify` object at runtime, but the
// TypeScript compiler is unaware of it. We declare its shape here so that
// `strict` compilation won't fail.
declare const Netlify: {
  env: {
    get(name: string): string | undefined;
  };
};

interface LetterData {
  id: string;
  user_id: string;
  plain_text: string;
  design_html: string;
  template_id: string;
  created_at: string;
}

/**
 * Create Supabase client for edge functions
 */
function createSupabaseClient() {
  const supabaseUrl = Netlify.env.get("NEXT_PUBLIC_SUPABASE_URL");
  const supabaseAnonKey = Netlify.env.get("NEXT_PUBLIC_SUPABASE_ANON_KEY");

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error("Missing Supabase configuration");
  }

  return createClient(supabaseUrl, supabase<PERSON><PERSON><PERSON>ey);
}

/**
 * Check if a PDF exists in Supabase storage for the given letter ID
 */
async function checkPdfExists(supabase: any, letterId: string): Promise<boolean> {
  try {
    const filePath = `${letterId}.pdf`;
    const { data, error } = await supabase.storage
      .from('generated-letters')
      .list('', { search: filePath });

    if (error) {
      console.error('Error checking PDF existence:', error);
      return false;
    }

    return data && data.length > 0;
  } catch (error) {
    console.error('Error checking PDF existence:', error);
    return false;
  }
}

/**
 * Download PDF from Supabase storage
 */
async function downloadPdfFromStorage(supabase: any, letterId: string): Promise<Uint8Array | null> {
  try {
    const filePath = `${letterId}.pdf`;
    const { data, error } = await supabase.storage
      .from('generated-letters')
      .download(filePath);

    if (error) {
      console.error('Error downloading PDF from storage:', error);
      return null;
    }

    return new Uint8Array(await data.arrayBuffer());
  } catch (error) {
    console.error('Error downloading PDF from storage:', error);
    return null;
  }
}

/**
 * Upload PDF to Supabase storage
 */
async function uploadPdfToStorage(supabase: any, letterId: string, pdfBuffer: Uint8Array): Promise<boolean> {
  try {
    const filePath = `${letterId}.pdf`;
    const { error } = await supabase.storage
      .from('generated-letters')
      .upload(filePath, pdfBuffer, {
        contentType: 'application/pdf',
        upsert: true, // Replace if exists
      });

    if (error) {
      console.error('Error uploading PDF to storage:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error uploading PDF to storage:', error);
    return false;
  }
}

/**
 * Generate PDF from letter data using Playwright with browserless.io
 */
async function generatePdf(letter: LetterData): Promise<Uint8Array> {
  const browserlessToken = Netlify.env.get("BROWSERLESS_IO_TOKEN");
  
  if (!browserlessToken) {
    throw new Error("Missing browserless.io token");
  }

  // Use browserless.io PDF endpoint
  const response = await fetch(`https://production-sfo.browserless.io/pdf?token=${browserlessToken}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      html: letter.design_html,
      options: {
        format: 'A4',
        printBackground: true,
        margin: {
          top: '0',
          right: '0',
          bottom: '0',
          left: '0',
        },
      },
      viewport: {
        width: 794, // A4 width in pixels (72 dpi)
        height: 1123, // A4 height in pixels (72 dpi)
        deviceScaleFactor: 2, // Higher scale for better quality
      },
      waitForSelector: 'body',
      waitForTimeout: 5000,
    }),
  });

  if (!response.ok) {
    throw new Error(`PDF generation failed: ${response.statusText}`);
  }

  const pdfBuffer = await response.arrayBuffer();
  return new Uint8Array(pdfBuffer);
}

export default async (request: Request) => {
  try {
    // Extract letter ID from URL path
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const letterId = pathParts[pathParts.length - 2]; // /api/edge/letters/{letterId}/download

    if (!letterId) {
      return new Response(
        JSON.stringify({ error: 'Letter ID is required' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Get access token from request header
    const accessToken = request.headers.get('x-supabase-auth');

    if (!accessToken) {
      return new Response(
        JSON.stringify({ error: 'Authentication required' }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Create Supabase client
    const supabase = createSupabaseClient();

    // Get user with the provided token
    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken);

    if (userError || !user) {
      console.error('Error getting user with token:', userError);
      return new Response(
        JSON.stringify({ error: 'Invalid authentication token' }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Get the letter from database
    const { data: letter, error: fetchError } = await supabase
      .from('letters')
      .select('id, user_id, plain_text, design_html, template_id, created_at')
      .eq('id', letterId)
      .eq('user_id', user.id) // Ensure user can only access their own letters
      .single();

    if (fetchError || !letter) {
      console.error('Error fetching letter:', fetchError);
      return new Response(
        JSON.stringify({ error: 'Letter not found or access denied' }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Check if PDF already exists in storage
    const pdfExists = await checkPdfExists(supabase, letterId);
    let pdfBuffer: Uint8Array;

    try {
      if (pdfExists) {
        // Download existing PDF from storage
        console.log(`PDF exists for letter ${letterId}, downloading from storage`);
        const existingPdf = await downloadPdfFromStorage(supabase, letterId);

        if (existingPdf) {
          pdfBuffer = existingPdf;
        } else {
          // If download fails, fall back to generating new PDF
          console.log(`Failed to download existing PDF for letter ${letterId}, generating new one`);
          pdfBuffer = await generatePdf(letter);
          // Try to upload the newly generated PDF
          await uploadPdfToStorage(supabase, letterId, pdfBuffer);
        }
      } else {
        // Generate new PDF
        console.log(`PDF does not exist for letter ${letterId}, generating new one`);
        pdfBuffer = await generatePdf(letter);
        // Upload to storage for future use
        const uploadSuccess = await uploadPdfToStorage(supabase, letterId, pdfBuffer);
        if (!uploadSuccess) {
          console.warn(`Failed to upload PDF to storage for letter ${letterId}`);
        }
      }
    } catch (pdfError) {
      console.error('PDF generation/retrieval error:', pdfError);
      return new Response(
        JSON.stringify({
          error: 'Failed to generate PDF. Please try downloading as HTML or text instead.'
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Return PDF as response
    return new Response(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="Surat_Lamaran_Gigsta_${letter.template_id || 'Letter'}.pdf"`,
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    });

  } catch (error) {
    console.error('Error downloading letter:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Failed to download letter. Please try again.'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

export const config: Config = {
  path: "/api/edge/letters/:letterId/download"
};
